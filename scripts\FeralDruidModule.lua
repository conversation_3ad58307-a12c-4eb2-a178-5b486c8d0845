local Tinkr, Bastion = ...

-- 创建模块
local FeralDruidModule = Bastion.Module:New('FeralDruidModule')

-- 获取玩家和目标单位
local Player = Bastion.UnitManager:Get('player')
local Target = Bastion.UnitManager:Get('target')

-- 创建法术书和物品书
local SpellBook = Bastion.Globals.SpellBook
local ItemBook = Bastion.Globals.ItemBook

-- 定义技能
-- 形态
local CatForm = SpellBook:GetSpell(768)               -- 猎豹形态
local BearForm = SpellBook:GetSpell(9634)             -- 熊形态
local TravelForm = SpellBook:GetSpell(783)            -- 旅行形态

-- 猫形态技能
local AutoAttack = SpellBook:GetSpell(6603)            -- 自动攻击
local SavageRoar = SpellBook:GetSpell(52610)         -- 野蛮咆哮
local Shred = SpellBook:GetSpell(48572)              -- 撕碎
local Rake = SpellBook:GetSpell(48574)               -- 斜掠
local Rip = SpellBook:GetSpell(49800)                -- 割裂
local Swipe = SpellBook:GetSpell(62078)              -- 横扫
local FerociousBite = SpellBook:GetSpell(48577)      -- 凶猛撕咬
local Mangle = SpellBook:GetSpell(48566)             -- 裂伤
local Prowl = SpellBook:GetSpell(5215)               -- 潜行
local Dash = SpellBook:GetSpell(33357)               -- 疾跑
local TigersFury = SpellBook:GetSpell(50213)         -- 猛虎之怒
local Cower = SpellBook:GetSpell(48575)               -- 畏缩
local Ravage = SpellBook:GetSpell(50334)             -- 狂暴
local OmenOfClarity = SpellBook:GetSpell(16870)      -- 清晰预兆

-- 通用技能
local FaerieFire = SpellBook:GetSpell(16857)         -- 精灵之火（野性）
local Innervate = SpellBook:GetSpell(29166)          -- 激活
local Rebirth = SpellBook:GetSpell(48477)            -- 复生
local MarkOfTheWild = SpellBook:GetSpell(48470)      -- 野性印记
local Thorns = SpellBook:GetSpell(53307)               -- 荆棘术

-- 寻找最佳目标
local BestTarget = Bastion.UnitManager:CreateCustomUnit('besttarget', function()
    -- 检查目标是否满足条件的函数
    local function IsValidTarget(unit)
        return unit:Exists()
            and unit:IsAlive()
            and unit:IsAffectingCombat()
            and Player:IsFacing(unit)
            and unit:InMelee(Player)
    end

    -- 使用find方法寻找第一个满足条件的敌人
    local target = Bastion.ObjectManager.enemies:find(function(unit)
        return IsValidTarget(unit)
    end)
    
    return target or Bastion.UnitManager:Get('none')
end)

-- 目标选择逻辑
local function CheckAndSetTarget()
    if not Target:Exists() 
        or Target:IsFriendly() 
        or not Target:IsAlive() then
        if BestTarget.unit then
            SetTargetObject(BestTarget.unit)
            return true
        end
    end
    return false
end

-- ===================== APL定义 =====================
local DefaultAPL = Bastion.APL:New('default')         -- 默认单体输出循环
-- local StealthAPL = Bastion.APL:New('stealth')         -- 潜行循环
-- local AoeAPL = Bastion.APL:New('aoe')                 -- AOE循环
-- local DefensiveAPL = Bastion.APL:New('defensive')     -- 防御循环

-- ===================== 默认循环 =====================
-- 变豹子
DefaultAPL:AddSpell(
    CatForm:CastableIf(function(self)
        return not Player:GetAuras():FindMy(CatForm):IsUp()
            and self:IsKnownAndUsable()
            and not Player:GetAuras():FindMy(OmenOfClarity):IsUp()
    end):SetTarget(Player)
)

-- 自动攻击
DefaultAPL:AddSpell(
    AutoAttack:CastableIf(function(self)
        return Target:Exists() 
            and Target:IsAlive()
            and Player:InMelee(Target)
            and not IsCurrentSpell(6603)
    end):SetTarget(Target)
)

-- 猛虎之怒
DefaultAPL:AddSpell(
    TigersFury:CastableIf(function(self)
        return self:IsKnownAndUsable()
            and Player:GetPower() < 30
            and not Player:GetAuras():FindMy(OmenOfClarity):IsUp()
    end):SetTarget(Player)
)

-- 割裂
DefaultAPL:AddSpell(
    Rip:CastableIf(function(self)
        return Target:Exists()
            and Player:GetComboPoints(Target) == 5
            and not Target:GetAuras():FindMy(Rip):IsUp()
            and self:IsKnownAndUsable()
    end):SetTarget(Target)
)

-- 野蛮咆哮
DefaultAPL:AddSpell(
    SavageRoar:CastableIf(function(self)
        return Target:Exists()
            and self:IsKnownAndUsable()
            and Player:GetComboPoints(Target) >= 1
            and (
                -- 条件1：咆哮剩余时间小于等于2秒
                (not Player:GetAuras():FindMy(SavageRoar):IsUp())
                
                -- 条件2：咆哮和割裂都存在，且符合特定的同步条件
                or (Player:GetAuras():FindMy(SavageRoar):IsUp() 
                    and Target:GetAuras():FindMy(Rip):IsUp()
                    and Player:GetAuras():FindMy(SavageRoar):GetRemainingTime() - Target:GetAuras():FindMy(Rip):GetRemainingTime() <= 4
                    and Target:GetAuras():FindMy(Rip):GetRemainingTime() + 25 <= 9 + 5 * Player:GetComboPoints(Target)
                    and (Player:GetComboPoints(Target) == 5 or not Player:GetAuras():FindMy(OmenOfClarity):IsUp())
                )
            )
    end):SetTarget(Target)
)

-- 凶猛撕咬
DefaultAPL:AddSpell(
    FerociousBite:CastableIf(function(self)
        return Target:Exists()
            and Player:GetComboPoints(Target) == 5
            and Player:GetPower() < 67
            and self:IsKnownAndUsable()
            and not Player:GetAuras():FindMy(OmenOfClarity):IsUp()
            and Target:GetAuras():FindMy(Rip):GetRemainingTime() >= 5
            and Player:GetAuras():FindMy(SavageRoar):GetRemainingTime() >= 5
    end):SetTarget(Target)
)

-- 裂伤（当无法在目标身后时使用）
DefaultAPL:AddSpell(
    Mangle:CastableIf(function(self)
        return Target:Exists()
            and not Target:GetAuras():FindMy(Mangle):IsUp()
            and self:IsKnownAndUsable()
            and not Player:GetAuras():FindMy(OmenOfClarity):IsUp()
    end):SetTarget(Target)
)

-- 斜掠
DefaultAPL:AddSpell(
    Rake:CastableIf(function(self)
        return Target:Exists()
            and not Target:GetAuras():FindMy(Rake):IsUp()
            and self:IsKnownAndUsable()
            and not Player:GetAuras():FindMy(OmenOfClarity):IsUp()
    end):SetTarget(Target)
)

-- 精灵之火
DefaultAPL:AddSpell(
    FaerieFire:CastableIf(function(self)
        return Target:Exists()
            and self:IsKnownAndUsable()
            and Player:GetPower() < 85
            and not Player:GetAuras():FindMy(OmenOfClarity):IsUp()
            and not (Player:GetComboPoints(Target) == 5 
                and Target:GetAuras():FindMy(Rip):IsUp()
                and Target:GetAuras():FindMy(Rip):GetRemainingTime() <= 2)
    end):SetTarget(Target)
)

-- 撕碎
DefaultAPL:AddSpell(
    Shred:CastableIf(function(self)
        return Target:Exists()
            and Player:IsBehind(Target)
            and self:IsKnownAndUsable()
            and (Player:GetAuras():FindMy(OmenOfClarity):IsUp()
                or (Player:GetComboPoints(Target) == 5 
                    and Target:GetAuras():FindMy(Rip):GetRemainingTime() < 2
                    and Player:GetPower() > 72)
                or (Player:GetComboPoints(Target) ~= 5 
                    or Target:GetAuras():FindMy(Rip):GetRemainingTime() >= 2)
                or Player:GetAuras():FindMy(Ravage):IsUp())
    end):SetTarget(Target)
)

FeralDruidModule:Sync(function()
    DefaultAPL:Execute()
end)

-- ===================== 注册模块 =====================
Bastion:Register(FeralDruidModule)